using MongoDB.Bson;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Serilog;

namespace RunMongoDB.Services
{
    public class MongoDBHealthMonitor
    {
        private readonly MongoDBSettings _settings;
        private readonly MongoClient _mongoClient;
        private DateTime _lastHealthCheck = DateTime.MinValue;
        private bool _isHealthy = false;

        public MongoDBHealthMonitor(MongoDBSettings settings)
        {
            _settings = settings;
            
            // Create MongoDB client with optimized settings
            var clientSettings = new MongoClientSettings
            {
                Server = new MongoServerAddress(_settings.Host, int.Parse(_settings.Port)),
                ConnectTimeout = TimeSpan.FromSeconds(_settings.ConnectionTimeoutSeconds),
                ServerSelectionTimeout = TimeSpan.FromSeconds(10),
                MaxConnectionPoolSize = _settings.MaxConnectionPoolSize,
                MinConnectionPoolSize = _settings.MinConnectionPoolSize,
                WaitQueueTimeout = TimeSpan.FromSeconds(30)
            };

            if (_settings.IsAuth == "true")
            {
                clientSettings.Credential = MongoCredential.CreateCredential(
                    "admin", _settings.UserName, _settings.PassWord);
            }

            _mongoClient = new MongoClient(clientSettings);
        }

        public async Task<HealthCheckResult> PerformHealthCheckAsync()
        {
            var result = new HealthCheckResult
            {
                CheckTime = DateTime.Now,
                IsHealthy = false
            };

            try
            {
                // Check if MongoDB process is running
                result.ProcessRunning = IsMongoDBProcessRunning();
                if (!result.ProcessRunning)
                {
                    result.Issues.Add("MongoDB process is not running");
                    return result;
                }

                // Check database connectivity
                result.DatabaseConnectable = await CheckDatabaseConnectivityAsync();
                if (!result.DatabaseConnectable)
                {
                    result.Issues.Add("Cannot connect to MongoDB database");
                    return result;
                }

                // Check server status
                var serverStatus = await GetServerStatusAsync();
                if (serverStatus != null)
                {
                    result.ServerStatus = serverStatus;
                    
                    // Check memory usage
                    result.MemoryUsageMB = GetMemoryUsage(serverStatus);
                    if (result.MemoryUsageMB > _settings.MaxMemoryUsageMB)
                    {
                        result.Issues.Add($"High memory usage: {result.MemoryUsageMB}MB (threshold: {_settings.MaxMemoryUsageMB}MB)");
                    }

                    // Check active connections
                    result.ActiveConnections = GetActiveConnections(serverStatus);
                    if (result.ActiveConnections > _settings.MaxActiveConnections)
                    {
                        result.Issues.Add($"High connection count: {result.ActiveConnections} (threshold: {_settings.MaxActiveConnections})");
                    }

                    // Check CPU usage
                    result.CpuUsagePercent = await GetCpuUsageAsync();
                    if (result.CpuUsagePercent > _settings.MaxCpuUsagePercent)
                    {
                        result.Issues.Add($"High CPU usage: {result.CpuUsagePercent:F1}% (threshold: {_settings.MaxCpuUsagePercent}%)");
                    }

                    // Check disk space
                    result.DiskSpaceInfo = await CheckDiskSpaceAsync();
                    if (result.DiskSpaceInfo.FreeSpacePercent < 10)
                    {
                        result.Issues.Add($"Low disk space: {result.DiskSpaceInfo.FreeSpacePercent:F1}% free");
                    }

                    // Check replication lag (if applicable)
                    result.ReplicationLag = await CheckReplicationLagAsync();
                    if (result.ReplicationLag > TimeSpan.FromMinutes(5))
                    {
                        result.Issues.Add($"High replication lag: {result.ReplicationLag.TotalMinutes:F1} minutes");
                    }
                }

                // Overall health assessment
                result.IsHealthy = result.ProcessRunning && 
                                 result.DatabaseConnectable && 
                                 result.Issues.Count == 0;

                _isHealthy = result.IsHealthy;
                _lastHealthCheck = result.CheckTime;

                if (result.IsHealthy)
                {
                    Log.Information("MongoDB health check passed");
                }
                else
                {
                    Log.Warning("MongoDB health check failed. Issues: {Issues}", string.Join(", ", result.Issues));
                }

                return result;
            }
            catch (Exception ex)
            {
                result.Issues.Add($"Health check error: {ex.Message}");
                Log.Error("Error during MongoDB health check: {Error}", ex.Message);
                return result;
            }
        }

        private bool IsMongoDBProcessRunning()
        {
            try
            {
                var processes = Process.GetProcessesByName("mongod");
                return processes.Length > 0;
            }
            catch
            {
                return false;
            }
        }

        private async Task<bool> CheckDatabaseConnectivityAsync()
        {
            try
            {
                var database = _mongoClient.GetDatabase("admin");
                var result = await database.RunCommandAsync<BsonDocument>(new BsonDocument("ping", 1));
                return result != null && result.Contains("ok") && result["ok"].ToDouble() == 1.0;
            }
            catch
            {
                return false;
            }
        }

        private async Task<BsonDocument> GetServerStatusAsync()
        {
            try
            {
                var database = _mongoClient.GetDatabase("admin");
                return await database.RunCommandAsync<BsonDocument>(new BsonDocument("serverStatus", 1));
            }
            catch (Exception ex)
            {
                Log.Error("Error getting server status: {Error}", ex.Message);
                return null;
            }
        }

        private long GetMemoryUsage(BsonDocument serverStatus)
        {
            try
            {
                if (serverStatus.Contains("mem") && serverStatus["mem"].IsBsonDocument)
                {
                    var mem = serverStatus["mem"].AsBsonDocument;
                    if (mem.Contains("resident"))
                    {
                        return mem["resident"].ToInt64();
                    }
                }
                return 0;
            }
            catch
            {
                return 0;
            }
        }

        private int GetActiveConnections(BsonDocument serverStatus)
        {
            try
            {
                if (serverStatus.Contains("connections") && serverStatus["connections"].IsBsonDocument)
                {
                    var connections = serverStatus["connections"].AsBsonDocument;
                    if (connections.Contains("current"))
                    {
                        return connections["current"].ToInt32();
                    }
                }
                return 0;
            }
            catch
            {
                return 0;
            }
        }

        private async Task<double> GetCpuUsageAsync()
        {
            try
            {
                var processes = Process.GetProcessesByName("mongod");
                if (processes.Length > 0)
                {
                    var process = processes[0];
                    var startTime = DateTime.UtcNow;
                    var startCpuUsage = process.TotalProcessorTime;
                    
                    await Task.Delay(1000); // Wait 1 second
                    
                    var endTime = DateTime.UtcNow;
                    var endCpuUsage = process.TotalProcessorTime;
                    
                    var cpuUsedMs = (endCpuUsage - startCpuUsage).TotalMilliseconds;
                    var totalMsPassed = (endTime - startTime).TotalMilliseconds;
                    var cpuUsageTotal = cpuUsedMs / (Environment.ProcessorCount * totalMsPassed);
                    
                    return cpuUsageTotal * 100;
                }
                return 0;
            }
            catch
            {
                return 0;
            }
        }

        private async Task<DiskSpaceInfo> CheckDiskSpaceAsync()
        {
            try
            {
                return await Task.Run(() =>
                {
                    var drive = new DriveInfo(Path.GetPathRoot(_settings.DataDirectory));
                    var totalSpace = drive.TotalSize;
                    var freeSpace = drive.AvailableFreeSpace;
                    var usedSpace = totalSpace - freeSpace;

                    return new DiskSpaceInfo
                    {
                        TotalSpaceGB = totalSpace / (1024.0 * 1024.0 * 1024.0),
                        FreeSpaceGB = freeSpace / (1024.0 * 1024.0 * 1024.0),
                        UsedSpaceGB = usedSpace / (1024.0 * 1024.0 * 1024.0),
                        FreeSpacePercent = (freeSpace * 100.0) / totalSpace
                    };
                });
            }
            catch
            {
                return new DiskSpaceInfo();
            }
        }

        private async Task<TimeSpan> CheckReplicationLagAsync()
        {
            try
            {
                var database = _mongoClient.GetDatabase("admin");
                var result = await database.RunCommandAsync<BsonDocument>(new BsonDocument("replSetGetStatus", 1));
                
                if (result != null && result.Contains("members"))
                {
                    var members = result["members"].AsBsonArray;
                    var primaryOpTime = DateTime.MinValue;
                    var secondaryOpTimes = new List<DateTime>();
                    
                    foreach (var member in members)
                    {
                        var memberDoc = member.AsBsonDocument;
                        if (memberDoc.Contains("state") && memberDoc.Contains("optimeDate"))
                        {
                            var state = memberDoc["state"].ToInt32();
                            var opTime = memberDoc["optimeDate"].ToUniversalTime();
                            
                            if (state == 1) // Primary
                            {
                                primaryOpTime = opTime;
                            }
                            else if (state == 2) // Secondary
                            {
                                secondaryOpTimes.Add(opTime);
                            }
                        }
                    }
                    
                    if (primaryOpTime != DateTime.MinValue && secondaryOpTimes.Count > 0)
                    {
                        var maxLag = secondaryOpTimes.Max(st => primaryOpTime - st);
                        return maxLag;
                    }
                }
                
                return TimeSpan.Zero;
            }
            catch
            {
                return TimeSpan.Zero;
            }
        }

        public bool IsHealthy => _isHealthy;
        public DateTime LastHealthCheck => _lastHealthCheck;
    }

    public class HealthCheckResult
    {
        public DateTime CheckTime { get; set; }
        public bool IsHealthy { get; set; }
        public bool ProcessRunning { get; set; }
        public bool DatabaseConnectable { get; set; }
        public long MemoryUsageMB { get; set; }
        public int ActiveConnections { get; set; }
        public double CpuUsagePercent { get; set; }
        public DiskSpaceInfo DiskSpaceInfo { get; set; } = new DiskSpaceInfo();
        public TimeSpan ReplicationLag { get; set; }
        public BsonDocument ServerStatus { get; set; }
        public List<string> Issues { get; set; } = new List<string>();
    }

    public class DiskSpaceInfo
    {
        public double TotalSpaceGB { get; set; }
        public double FreeSpaceGB { get; set; }
        public double UsedSpaceGB { get; set; }
        public double FreeSpacePercent { get; set; }
    }
}
