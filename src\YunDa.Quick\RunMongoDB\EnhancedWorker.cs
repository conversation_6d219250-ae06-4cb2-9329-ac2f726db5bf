using Microsoft.Extensions.Hosting;
using MongoDB.Bson;
using MongoDB.Driver;
using Serilog;
using System;
using System.Threading;
using System.Threading.Tasks;
using RunMongoDB.Services;
using System.IO;

namespace RunMongoDB
{
    public class EnhancedWorker
    {
        private readonly MongoDBSettings _settings;
        private readonly MongoDBProcessManager _processManager;
        private readonly MongoDBHealthMonitor _healthMonitor;
        private readonly MongoDBInitializer _initializer;
        private readonly MongoDBPerformanceOptimizer _performanceOptimizer;
        
        private bool _isInitialized = false;
        private DateTime _lastPerformanceCheck = DateTime.MinValue;
        private CancellationTokenSource _cancellationTokenSource;
        private bool _isRunning = false;

        public EnhancedWorker(MongoDBSettings settings)
        {
            _settings = settings;
            _cancellationTokenSource = new CancellationTokenSource();

            // Initialize services
            _processManager = new MongoDBProcessManager(_settings);
            _healthMonitor = new MongoDBHealthMonitor(_settings);
            _initializer = new MongoDBInitializer(_settings);
            _performanceOptimizer = new MongoDBPerformanceOptimizer(_settings);

            Log.Information("Enhanced MongoDB Worker initialized with advanced monitoring and recovery capabilities");
        }

        public async Task ExecuteAsync()
        {
            _isRunning = true;
            Log.Information("Starting enhanced MongoDB monitoring and management service");

            try
            {
                // Initial setup and validation
                await InitialSetupAsync();

                // Start main monitoring loop
                await MonitoringLoopAsync(_cancellationTokenSource.Token);
            }
            catch (Exception ex)
            {
                Log.Fatal("Critical error in MongoDB worker: {Error}", ex.Message);
                throw;
            }
            finally
            {
                _isRunning = false;
                Log.Information("Enhanced MongoDB Worker stopped");
            }
        }

        private async Task InitialSetupAsync()
        {
            try
            {
                Log.Information("Performing initial MongoDB setup and validation");

                // Ensure MongoDB is running
                if (!_processManager.IsMongoDBRunning())
                {
                    Log.Information("MongoDB is not running. Starting MongoDB...");
                    var started = await _processManager.StartMongoDBAsync();
                    
                    if (!started)
                    {
                        throw new Exception("Failed to start MongoDB during initial setup");
                    }
                }

                // Wait for MongoDB to be ready and initialize database
                await Task.Delay(5000); // Give MongoDB time to fully start
                
                if (!_isInitialized)
                {
                    Log.Information("Initializing MongoDB database for first-time setup");
                    var initialized = await _initializer.InitializeDatabaseAsync();
                    
                    if (initialized)
                    {
                        _isInitialized = true;
                        Log.Information("MongoDB database initialization completed successfully");
                    }
                    else
                    {
                        Log.Warning("MongoDB database initialization failed, but continuing with monitoring");
                    }
                }

                Log.Information("Initial MongoDB setup completed");
            }
            catch (Exception ex)
            {
                Log.Error("Error during initial MongoDB setup: {Error}", ex.Message);
                throw;
            }
        }

        private async Task MonitoringLoopAsync(CancellationToken cancellationToken)
        {
            Log.Information("Starting MongoDB monitoring loop");

            while (!cancellationToken.IsCancellationRequested)
            {
                try
                {
                    // Check if MongoDB process is running
                    if (!_processManager.IsMongoDBRunning())
                    {
                        Log.Warning("MongoDB process not detected. Attempting to restart...");
                        await HandleMongoDBCrashAsync();
                    }
                    else
                    {
                        // Perform health check
                        await PerformHealthCheckAsync();
                        
                        // Perform performance optimization if needed
                        await PerformPerformanceOptimizationAsync();
                    }

                    // Wait before next check
                    await Task.Delay(_settings.ProcessCheckIntervalSeconds * 1000, cancellationToken);
                }
                catch (OperationCanceledException)
                {
                    Log.Information("MongoDB monitoring loop cancelled");
                    break;
                }
                catch (Exception ex)
                {
                    Log.Error("Error in MongoDB monitoring loop: {Error}", ex.Message);
                    
                    // Wait a bit longer on error to avoid rapid error loops
                    await Task.Delay(10000, cancellationToken);
                }
            }
        }

        private async Task HandleMongoDBCrashAsync()
        {
            try
            {
                Log.Warning("MongoDB crash detected. Initiating recovery procedures...");

                // Perform data integrity checks before restart
                await PerformDataIntegrityChecksAsync();

                // Attempt to restart MongoDB
                var restarted = await _processManager.RestartMongoDBAsync();
                
                if (restarted)
                {
                    Log.Information("MongoDB successfully restarted after crash");
                    
                    // Wait for MongoDB to be ready
                    await Task.Delay(10000);
                    
                    // Verify database integrity after restart
                    await VerifyDatabaseIntegrityAsync();
                }
                else
                {
                    Log.Error("Failed to restart MongoDB after crash. Manual intervention may be required.");
                    
                    // Send alert if configured
                    await SendCrashAlertAsync("MongoDB restart failed after crash");
                }
            }
            catch (Exception ex)
            {
                Log.Error("Error during MongoDB crash recovery: {Error}", ex.Message);
                await SendCrashAlertAsync($"MongoDB crash recovery failed: {ex.Message}");
            }
        }

        private async Task PerformHealthCheckAsync()
        {
            try
            {
                var healthResult = await _healthMonitor.PerformHealthCheckAsync();
                
                if (!healthResult.IsHealthy)
                {
                    Log.Warning("MongoDB health check failed. Issues detected: {Issues}", 
                        string.Join(", ", healthResult.Issues));
                    
                    // Take corrective actions based on issues
                    await HandleHealthIssuesAsync(healthResult);
                }
                else
                {
                    Log.Debug("MongoDB health check passed - all systems normal");
                }
            }
            catch (Exception ex)
            {
                Log.Error("Error during MongoDB health check: {Error}", ex.Message);
            }
        }

        private async Task HandleHealthIssuesAsync(HealthCheckResult healthResult)
        {
            try
            {
                foreach (var issue in healthResult.Issues)
                {
                    if (issue.Contains("High memory usage"))
                    {
                        Log.Information("Attempting to optimize memory usage");
                        await _performanceOptimizer.OptimizePerformanceAsync();
                    }
                    else if (issue.Contains("High connection count"))
                    {
                        Log.Information("High connection count detected - monitoring connection pool");
                        // Could implement connection pool optimization here
                    }
                    else if (issue.Contains("High CPU usage"))
                    {
                        Log.Information("High CPU usage detected - checking for long-running operations");
                        // Could implement query optimization here
                    }
                    else if (issue.Contains("Low disk space"))
                    {
                        Log.Warning("Low disk space detected - consider cleanup or expansion");
                        await SendCrashAlertAsync("MongoDB server running low on disk space");
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error("Error handling health issues: {Error}", ex.Message);
            }
        }

        private async Task PerformPerformanceOptimizationAsync()
        {
            try
            {
                var timeSinceLastOptimization = DateTime.Now - _lastPerformanceCheck;
                
                if (timeSinceLastOptimization.TotalHours >= _settings.OptimizationIntervalHours)
                {
                    Log.Information("Performing scheduled performance optimization");
                    
                    var optimized = await _performanceOptimizer.OptimizePerformanceAsync();
                    
                    if (optimized)
                    {
                        _lastPerformanceCheck = DateTime.Now;
                        Log.Information("Performance optimization completed successfully");
                    }
                    else
                    {
                        Log.Warning("Performance optimization encountered issues");
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error("Error during performance optimization: {Error}", ex.Message);
            }
        }

        private async Task PerformDataIntegrityChecksAsync()
        {
            try
            {
                await Task.Run(() =>
                {
                    Log.Information("Performing data integrity checks before MongoDB restart");

                    // Check for corrupted data files
                    var dataDirectory = _settings.DataDirectory;
                    if (Directory.Exists(dataDirectory))
                    {
                        var lockFile = Path.Combine(dataDirectory, "mongod.lock");
                        if (File.Exists(lockFile))
                        {
                            Log.Information("Removing stale MongoDB lock file");
                            File.Delete(lockFile);
                        }
                    }

                    // Additional integrity checks could be added here
                    Log.Information("Data integrity checks completed");
                });
            }
            catch (Exception ex)
            {
                Log.Error("Error during data integrity checks: {Error}", ex.Message);
            }
        }

        private async Task VerifyDatabaseIntegrityAsync()
        {
            try
            {
                Log.Information("Verifying database integrity after restart");
                
                // Re-initialize if needed
                if (!_isInitialized)
                {
                    await _initializer.InitializeDatabaseAsync();
                    _isInitialized = true;
                }

                // Perform health check to verify everything is working
                var healthResult = await _healthMonitor.PerformHealthCheckAsync();
                
                if (healthResult.IsHealthy)
                {
                    Log.Information("Database integrity verification passed");
                }
                else
                {
                    Log.Warning("Database integrity verification found issues: {Issues}", 
                        string.Join(", ", healthResult.Issues));
                }
            }
            catch (Exception ex)
            {
                Log.Error("Error during database integrity verification: {Error}", ex.Message);
            }
        }

        private async Task SendCrashAlertAsync(string message)
        {
            try
            {
                await Task.Run(() =>
                {
                    if (_settings.EnableEmailAlerts && !string.IsNullOrEmpty(_settings.AlertEmailRecipients))
                    {
                        Log.Information("Sending crash alert: {Message}", message);
                        // Email alert implementation would go here
                        // For now, just log the alert
                    }

                    // Log critical alert
                    Log.Error("CRITICAL ALERT: {Message}", message);
                });
            }
            catch (Exception ex)
            {
                Log.Error("Error sending crash alert: {Error}", ex.Message);
            }
        }

        public void Stop()
        {
            Log.Information("Stopping Enhanced MongoDB Worker...");
            _cancellationTokenSource?.Cancel();
        }

        public void Dispose()
        {
            _cancellationTokenSource?.Dispose();
            _processManager?.Dispose();
        }

        public bool IsRunning => _isRunning;
    }
}
