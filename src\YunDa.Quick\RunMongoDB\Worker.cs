using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MongoDB.Bson;
using MongoDB.Driver;
using Serilog;
using System;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace RunMongoDB
{
    public class Worker
    {
        private readonly MongoDBSettings _mongoDBSettings;
        private readonly MongoClient _mongoClient;
        private int _restartAttempts = 0;
        private DateTime _lastOptimizationTime = DateTime.MinValue;

        public Worker(MongoDBSettings mongoDBSettings)
        {
            _mongoDBSettings = mongoDBSettings ?? throw new ArgumentNullException(nameof(mongoDBSettings));

            Log.Information("Initializing MongoDB Worker with settings from configuration");

            // Create MongoDB client connection
            _mongoClient = CreateMongoClient();

            // Ensure required directories exist
            EnsureDirectoriesExist();

            Log.Information("MongoDB Worker initialized successfully");
        }

        private MongoClient CreateMongoClient()
        {
            var connectionString = $"mongodb://{_mongoDBSettings.Host}:{_mongoDBSettings.Port}";

            if (_mongoDBSettings.IsAuth == "true")
            {
                Log.Information("Creating authenticated MongoDB connection to {Host}:{Port}",
                    _mongoDBSettings.Host, _mongoDBSettings.Port);

                var credentials = MongoCredential.CreateCredential("admin",
                    _mongoDBSettings.UserName, _mongoDBSettings.PassWord);

                return new MongoClient(new MongoClientSettings
                {
                    Server = new MongoServerAddress(_mongoDBSettings.Host, int.Parse(_mongoDBSettings.Port)),
                    Credential = credentials,
                    ConnectTimeout = TimeSpan.FromSeconds(_mongoDBSettings.ConnectionTimeoutSeconds),
                    MaxConnectionPoolSize = _mongoDBSettings.MaxConnectionPoolSize,
                    MinConnectionPoolSize = _mongoDBSettings.MinConnectionPoolSize
                });
            }
            else
            {
                Log.Information("Creating non-authenticated MongoDB connection to {ConnectionString}", connectionString);
                return new MongoClient(connectionString);
            }
        }

        private void EnsureDirectoriesExist()
        {
            var directories = new[]
            {
                _mongoDBSettings.DataDirectory,
                _mongoDBSettings.LogDirectory,
                _mongoDBSettings.BackupDirectory
            };

            foreach (var directory in directories)
            {
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                    Log.Information("Created directory: {Directory}", directory);
                }
            }
        }

        public void Execute()
        {
            Log.Information("Starting MongoDB monitoring loop");
            Log.Information("Process check interval: {Interval}s", _mongoDBSettings.ProcessCheckIntervalSeconds);

            while (true)
            {
                try
                {
                    var mongoProcesses = Process.GetProcessesByName("mongod");

                    if (mongoProcesses != null && mongoProcesses.Length > 0)
                    {
                        Log.Debug("Found {ProcessCount} MongoDB process(es) running", mongoProcesses.Length);

                        if (!CheckMongoDbHealth())
                        {
                            Log.Warning("MongoDB health check failed. Attempting restart at {Time}", DateTimeOffset.Now);

                            if (_restartAttempts < _mongoDBSettings.MaxRestartAttempts)
                            {
                                _restartAttempts++;
                                Log.Information("Restart attempt {Attempt}/{MaxAttempts}",
                                    _restartAttempts, _mongoDBSettings.MaxRestartAttempts);

                                StopMongoDb(mongoProcesses);
                                Task.Delay(_mongoDBSettings.RestartDelaySeconds * 1000).Wait();
                                StartMongoDb();
                            }
                            else
                            {
                                Log.Error("Maximum restart attempts ({MaxAttempts}) reached. Manual intervention required.",
                                    _mongoDBSettings.MaxRestartAttempts);
                                Task.Delay(60000).Wait(); // Wait 1 minute before trying again
                                _restartAttempts = 0; // Reset counter
                            }
                        }
                        else
                        {
                            _restartAttempts = 0; // Reset counter on successful health check
                            Log.Debug("MongoDB health check passed");
                        }
                    }
                    else
                    {
                        Log.Warning("No MongoDB processes found. Starting MongoDB...");
                        StartMongoDb();
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, "Error in monitoring loop");
                }

                Task.Delay(_mongoDBSettings.ProcessCheckIntervalSeconds * 1000).Wait();
            }
        }
        private bool StopMongoDb(Process[] processes)
        {
            try
            {
                Log.Information("Stopping {ProcessCount} MongoDB process(es)", processes.Length);

                foreach (var process in processes)
                {
                    try
                    {
                        Log.Information("Stopping MongoDB process with PID: {ProcessId}", process.Id);
                        process.Kill(true);

                        // Wait for process to exit
                        if (!process.WaitForExit(10000)) // 10 seconds timeout
                        {
                            Log.Warning("Process {ProcessId} did not exit within timeout", process.Id);
                        }
                        else
                        {
                            Log.Information("Process {ProcessId} stopped successfully", process.Id);
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex, "Failed to stop MongoDB process {ProcessId}", process.Id);
                    }
                    finally
                    {
                        process.Dispose();
                    }
                }

                Log.Information("MongoDB stop operation completed");
                return true;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Failed to stop MongoDB processes");
                return false;
            }
        }
        private bool StartMongoDb()
        {
            try
            {
                Log.Information("Starting MongoDB server...");

                // Clean up lock files and diagnostic data
                CleanupMongoDbFiles();

                var processStartInfo = new ProcessStartInfo
                {
                    WorkingDirectory = Path.GetDirectoryName(_mongoDBSettings.MongoDBExecutablePath),
                    FileName = _mongoDBSettings.MongoDBExecutablePath,
                    CreateNoWindow = true,
                    UseShellExecute = false,
                    RedirectStandardOutput = false,
                    RedirectStandardError = false
                };

                processStartInfo.ArgumentList.Add("--config");
                processStartInfo.ArgumentList.Add(_mongoDBSettings.MongoDBConfigPath);

                Log.Information("Starting MongoDB with executable: {ExecutablePath}", _mongoDBSettings.MongoDBExecutablePath);
                Log.Information("Using config file: {ConfigPath}", _mongoDBSettings.MongoDBConfigPath);
                Log.Information("Working directory: {WorkingDirectory}", processStartInfo.WorkingDirectory);
                Task.Delay(2000).Wait();
                var process = Process.Start(processStartInfo);

                if (process != null)
                {
                    Log.Information("MongoDB process started with PID: {ProcessId}", process.Id);

                    // Wait a moment for the process to initialize
                    Task.Delay(2000).Wait();

                    // Verify the process is still running
                    if (!process.HasExited)
                    {
                        Log.Information("MongoDB server started successfully");
                        return true;
                    }
                    else
                    {
                        Log.Error("MongoDB process exited immediately with code: {ExitCode}", process.ExitCode);
                        return false;
                    }
                }
                else
                {
                    Log.Error("Failed to start MongoDB process");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Failed to start MongoDB server");
                return false;
            }
        }

        private void CleanupMongoDbFiles()
        {
            try
            {
                // Remove lock file
                var lockFile = Path.Combine(_mongoDBSettings.DataDirectory, "mongod.lock");
                if (File.Exists(lockFile))
                {
                    File.Delete(lockFile);
                    Log.Information("Removed MongoDB lock file: {LockFile}", lockFile);
                }

                // Clean diagnostic data
                var diagnosticPath = Path.Combine(_mongoDBSettings.DataDirectory, "diagnostic.data");
                if (Directory.Exists(diagnosticPath))
                {
                    var files = Directory.GetFiles(diagnosticPath);
                    foreach (var file in files)
                    {
                        try
                        {
                            File.Delete(file);
                        }
                        catch (Exception ex)
                        {
                            Log.Warning(ex, "Could not delete diagnostic file: {File}", file);
                        }
                    }
                    Log.Information("Cleaned {FileCount} diagnostic files", files.Length);
                }

                // Remove old log file
                var mongodLogFile = Path.Combine(_mongoDBSettings.LogDirectory, "mongod.log");
                if (File.Exists(mongodLogFile))
                {
                    try
                    {
                        File.Delete(mongodLogFile);
                        Log.Information("Removed old MongoDB log file: {LogFile}", mongodLogFile);
                    }
                    catch (Exception ex)
                    {
                        Log.Warning(ex, "Could not delete MongoDB log file: {LogFile}", mongodLogFile);
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "Error during MongoDB file cleanup");
            }
        }
        private bool CheckMongoDbHealth()
        {
            try
            {
                Log.Debug("Performing MongoDB health check...");

                // Use Ping command to test MongoDB server connectivity
                var serverStatus = _mongoClient.GetDatabase("admin").RunCommand<BsonDocument>(new BsonDocument("ping", 1));

                if (serverStatus != null && serverStatus.Contains("ok") && serverStatus["ok"].ToDouble() == 1.0)
                {
                    Log.Debug("MongoDB ping successful");

                    // Perform periodic optimization if enabled
                    if (_mongoDBSettings.EnablePerformanceOptimization)
                    {
                        var timeSinceLastOptimization = DateTime.Now - _lastOptimizationTime;
                        if (timeSinceLastOptimization.TotalHours >= _mongoDBSettings.OptimizationIntervalHours)
                        {
                            Log.Information("Performing scheduled MongoDB optimization");
                            OptimizeMongoDb();
                            _lastOptimizationTime = DateTime.Now;
                        }
                    }

                    return true;
                }
                else
                {
                    Log.Warning("MongoDB ping returned unexpected result: {Result}", serverStatus?.ToString());
                    return false;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "MongoDB health check failed");
                return false;
            }
        }
        private void OptimizeMongoDb()
        {
            try
            {
                Log.Information("Starting MongoDB database optimization for: {DatabaseName}", _mongoDBSettings.DatabaseName);

                // Get database instance
                var database = _mongoClient.GetDatabase(_mongoDBSettings.DatabaseName);

                // Get all collections in the database
                var collections = database.ListCollectionNames().ToList();

                if (collections.Count == 0)
                {
                    Log.Information("No collections found in database {DatabaseName}", _mongoDBSettings.DatabaseName);
                    return;
                }

                Log.Information("Found {CollectionCount} collections to optimize", collections.Count);

                // Perform compact operation on each collection
                var optimizedCount = 0;
                foreach (var collectionName in collections)
                {
                    try
                    {
                        Log.Information("Optimizing collection: {CollectionName}", collectionName);

                        // Create compact command
                        var command = new BsonDocument { { "compact", collectionName } };

                        // Execute compact command
                        var result = database.RunCommand<BsonDocument>(command);

                        if (result != null && result.Contains("ok") && result["ok"].ToDouble() == 1.0)
                        {
                            optimizedCount++;
                            Log.Information("Collection {CollectionName} optimization completed successfully", collectionName);
                        }
                        else
                        {
                            Log.Warning("Collection {CollectionName} optimization returned unexpected result: {Result}",
                                collectionName, result?.ToString());
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex, "Failed to optimize collection: {CollectionName}", collectionName);
                    }
                }

                Log.Information("Database optimization completed. Successfully optimized {OptimizedCount}/{TotalCount} collections",
                    optimizedCount, collections.Count);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "MongoDB database optimization failed");
            }
        }
       
    }
}
