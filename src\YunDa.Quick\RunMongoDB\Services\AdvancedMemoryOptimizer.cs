using System;
using System.Diagnostics;
using System.Threading.Tasks;
using System.Runtime;
using System.Runtime.InteropServices;
using MongoDB.Driver;
using MongoDB.Bson;
using Serilog;

namespace RunMongoDB.Services
{
    /// <summary>
    /// Advanced memory optimization service for MongoDB process and system memory management
    /// Focuses on stability and performance over data protection
    /// </summary>
    public class AdvancedMemoryOptimizer
    {
        private readonly MongoDBSettings _settings;
        private readonly MongoClient _mongoClient;
        private DateTime _lastOptimizationTime = DateTime.MinValue;
        private long _lastMemoryUsage = 0;
        private bool _isOptimizationInProgress = false;

        // Windows API for memory management
        [DllImport("kernel32.dll")]
        private static extern bool SetProcessWorkingSetSize(IntPtr hProcess, UIntPtr dwMinimumWorkingSetSize, UIntPtr dwMaximumWorkingSetSize);

        [DllImport("kernel32.dll")]
        private static extern IntPtr GetCurrentProcess();

        public AdvancedMemoryOptimizer(MongoDBSettings settings)
        {
            _settings = settings;
            
            // Create MongoDB client for memory optimization commands
            var connectionString = $"mongodb://{_settings.Host}:{_settings.Port}";
            _mongoClient = new MongoClient(connectionString);
            
            Log.Information("Advanced Memory Optimizer initialized with aggressive optimization settings");
        }

        /// <summary>
        /// Performs comprehensive memory optimization for MongoDB and system
        /// </summary>
        public async Task<bool> OptimizeMemoryAsync()
        {
            if (_isOptimizationInProgress)
            {
                Log.Debug("Memory optimization already in progress, skipping");
                return true;
            }

            try
            {
                _isOptimizationInProgress = true;
                Log.Information("Starting advanced memory optimization");

                var optimizationTasks = new[]
                {
                    OptimizeMongoDBMemoryAsync(),
                    OptimizeSystemMemoryAsync(),
                    OptimizeProcessMemoryAsync(),
                    OptimizeGarbageCollectionAsync()
                };

                var results = await Task.WhenAll(optimizationTasks);
                var allSuccessful = Array.TrueForAll(results, r => r);

                if (allSuccessful)
                {
                    _lastOptimizationTime = DateTime.Now;
                    Log.Information("Advanced memory optimization completed successfully");
                }
                else
                {
                    Log.Warning("Some memory optimization tasks failed");
                }

                return allSuccessful;
            }
            catch (Exception ex)
            {
                Log.Error("Error during advanced memory optimization: {Error}", ex.Message);
                return false;
            }
            finally
            {
                _isOptimizationInProgress = false;
            }
        }

        /// <summary>
        /// Optimizes MongoDB-specific memory usage
        /// </summary>
        private async Task<bool> OptimizeMongoDBMemoryAsync()
        {
            try
            {
                Log.Information("Optimizing MongoDB memory usage");

                var database = _mongoClient.GetDatabase("admin");

                // Disable data validation for performance (stability over data protection)
                await DisableDataValidationAsync(database);

                // Optimize cache settings for performance
                await OptimizeCacheSettingsAsync(database);

                // Clear query plan cache to free memory
                await ClearQueryPlanCacheAsync(database);

                // Compact collections to reduce memory fragmentation
                await CompactCollectionsAsync();

                // Force MongoDB to release unused memory
                await ForceMemoryReleaseAsync(database);

                Log.Information("MongoDB memory optimization completed");
                return true;
            }
            catch (Exception ex)
            {
                Log.Error("Error optimizing MongoDB memory: {Error}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// Disables data validation features for better performance
        /// </summary>
        private async Task DisableDataValidationAsync(IMongoDatabase database)
        {
            try
            {
                if (!_settings.EnableDataValidation)
                {
                    Log.Information("Disabling data validation for performance optimization");
                    
                    // Disable schema validation
                    var command = new BsonDocument
                    {
                        { "setParameter", 1 },
                        { "enableSchemaValidation", false }
                    };
                    
                    await database.RunCommandAsync<BsonDocument>(command);
                    Log.Debug("Schema validation disabled");
                }
            }
            catch (Exception ex)
            {
                Log.Debug("Could not disable data validation: {Error}", ex.Message);
            }
        }

        /// <summary>
        /// Optimizes MongoDB cache settings for memory efficiency
        /// </summary>
        private async Task OptimizeCacheSettingsAsync(IMongoDatabase database)
        {
            try
            {
                Log.Information("Optimizing MongoDB cache settings");

                // Set cache size to target memory usage
                var cacheSize = _settings.TargetMemoryUsageMB * 0.7; // 70% of target for cache
                
                var command = new BsonDocument
                {
                    { "setParameter", 1 },
                    { "wiredTigerCacheSizeGB", cacheSize / 1024.0 }
                };
                
                await database.RunCommandAsync<BsonDocument>(command);
                Log.Information("Cache size optimized to {CacheSize}GB", cacheSize / 1024.0);
            }
            catch (Exception ex)
            {
                Log.Debug("Could not optimize cache settings: {Error}", ex.Message);
            }
        }

        /// <summary>
        /// Clears query plan cache to free memory
        /// </summary>
        private async Task ClearQueryPlanCacheAsync(IMongoDatabase database)
        {
            try
            {
                var command = new BsonDocument("planCacheClear", 1);
                await database.RunCommandAsync<BsonDocument>(command);
                Log.Debug("Query plan cache cleared");
            }
            catch (Exception ex)
            {
                Log.Debug("Could not clear query plan cache: {Error}", ex.Message);
            }
        }

        /// <summary>
        /// Compacts collections to reduce memory fragmentation
        /// </summary>
        private async Task CompactCollectionsAsync()
        {
            try
            {
                if (!_settings.EnableMemoryCompaction)
                    return;

                Log.Information("Compacting collections to reduce memory fragmentation");
                
                var database = _mongoClient.GetDatabase(_settings.DatabaseName);
                var collections = await database.ListCollectionNamesAsync();
                var collectionList = await collections.ToListAsync();

                foreach (var collectionName in collectionList)
                {
                    try
                    {
                        var command = new BsonDocument("compact", collectionName);
                        await database.RunCommandAsync<BsonDocument>(command);
                        Log.Debug("Compacted collection: {CollectionName}", collectionName);
                    }
                    catch (Exception ex)
                    {
                        Log.Debug("Could not compact collection {CollectionName}: {Error}", 
                            collectionName, ex.Message);
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Warning("Error during collection compaction: {Error}", ex.Message);
            }
        }

        /// <summary>
        /// Forces MongoDB to release unused memory
        /// </summary>
        private async Task ForceMemoryReleaseAsync(IMongoDatabase database)
        {
            try
            {
                // Force memory release
                var command = new BsonDocument
                {
                    { "setParameter", 1 },
                    { "wiredTigerEngineRuntimeConfig", "cache_size=1GB" }
                };
                
                await database.RunCommandAsync<BsonDocument>(command);
                
                // Reset to normal size
                await Task.Delay(1000);
                
                var resetCommand = new BsonDocument
                {
                    { "setParameter", 1 },
                    { "wiredTigerEngineRuntimeConfig", $"cache_size={_settings.TargetMemoryUsageMB * 0.7 / 1024.0}GB" }
                };
                
                await database.RunCommandAsync<BsonDocument>(resetCommand);
                Log.Debug("Forced memory release completed");
            }
            catch (Exception ex)
            {
                Log.Debug("Could not force memory release: {Error}", ex.Message);
            }
        }

        /// <summary>
        /// Optimizes system memory usage
        /// </summary>
        private async Task<bool> OptimizeSystemMemoryAsync()
        {
            try
            {
                Log.Information("Optimizing system memory usage");

                // Trim working set of current process
                await TrimProcessWorkingSetAsync();

                // Optimize MongoDB process memory
                await OptimizeMongoDBProcessMemoryAsync();

                Log.Information("System memory optimization completed");
                return true;
            }
            catch (Exception ex)
            {
                Log.Error("Error optimizing system memory: {Error}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// Trims the working set of the current process
        /// </summary>
        private async Task TrimProcessWorkingSetAsync()
        {
            try
            {
                await Task.Run(() =>
                {
                    var currentProcess = GetCurrentProcess();
                    SetProcessWorkingSetSize(currentProcess, UIntPtr.Zero, UIntPtr.Zero);
                });
                
                Log.Debug("Process working set trimmed");
            }
            catch (Exception ex)
            {
                Log.Debug("Could not trim process working set: {Error}", ex.Message);
            }
        }

        /// <summary>
        /// Optimizes MongoDB process memory usage
        /// </summary>
        private async Task OptimizeMongoDBProcessMemoryAsync()
        {
            try
            {
                await Task.Run(() =>
                {
                    var mongoProcesses = Process.GetProcessesByName("mongod");

                    foreach (var process in mongoProcesses)
                    {
                        try
                        {
                            // Trim working set
                            SetProcessWorkingSetSize(process.Handle, UIntPtr.Zero, UIntPtr.Zero);

                            // Check memory usage
                            var memoryUsage = process.WorkingSet64 / 1024 / 1024; // MB
                            Log.Debug("MongoDB process {ProcessId} memory usage: {MemoryUsage}MB",
                                process.Id, memoryUsage);

                            _lastMemoryUsage = memoryUsage;
                        }
                        catch (Exception ex)
                        {
                            Log.Debug("Could not optimize MongoDB process {ProcessId}: {Error}",
                                process.Id, ex.Message);
                        }
                        finally
                        {
                            process.Dispose();
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                Log.Warning("Error optimizing MongoDB process memory: {Error}", ex.Message);
            }
        }

        /// <summary>
        /// Optimizes process memory allocation
        /// </summary>
        private async Task<bool> OptimizeProcessMemoryAsync()
        {
            try
            {
                Log.Information("Optimizing process memory allocation");

                await Task.Run(() =>
                {
                    // Set GC to server mode for better performance
                    if (_settings.EnableGarbageCollectionOptimization)
                    {
                        GCSettings.LatencyMode = GCLatencyMode.SustainedLowLatency;
                        Log.Debug("GC latency mode set to SustainedLowLatency");
                    }
                });

                return true;
            }
            catch (Exception ex)
            {
                Log.Error("Error optimizing process memory: {Error}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// Optimizes garbage collection for better memory management
        /// </summary>
        private async Task<bool> OptimizeGarbageCollectionAsync()
        {
            try
            {
                if (!_settings.EnableGarbageCollectionOptimization)
                    return true;

                Log.Information("Optimizing garbage collection");

                await Task.Run(() =>
                {
                    // Force full garbage collection
                    GC.Collect();
                    GC.WaitForPendingFinalizers();
                    GC.Collect();
                    
                    // Compact large object heap
                    GCSettings.LargeObjectHeapCompactionMode = GCLargeObjectHeapCompactionMode.CompactOnce;
                    GC.Collect();
                });

                Log.Debug("Garbage collection optimization completed");
                return true;
            }
            catch (Exception ex)
            {
                Log.Error("Error optimizing garbage collection: {Error}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// Checks if memory optimization is needed
        /// </summary>
        public bool ShouldOptimizeMemory()
        {
            try
            {
                var timeSinceLastOptimization = DateTime.Now - _lastOptimizationTime;
                
                if (timeSinceLastOptimization.TotalMinutes >= _settings.MemoryOptimizationIntervalMinutes)
                {
                    return true;
                }

                // Check memory pressure
                var memoryPressure = GetCurrentMemoryPressure();
                if (memoryPressure > _settings.MemoryPressureThresholdPercent)
                {
                    Log.Information("Memory pressure detected: {MemoryPressure}%", memoryPressure);
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                Log.Error("Error checking if memory optimization is needed: {Error}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// Gets current memory pressure percentage
        /// </summary>
        private double GetCurrentMemoryPressure()
        {
            try
            {
                var mongoProcesses = Process.GetProcessesByName("mongod");
                if (mongoProcesses.Length == 0)
                    return 0.0;

                var totalMemoryUsage = 0L;
                foreach (var process in mongoProcesses)
                {
                    try
                    {
                        totalMemoryUsage += process.WorkingSet64;
                    }
                    finally
                    {
                        process.Dispose();
                    }
                }

                var memoryUsageMB = totalMemoryUsage / 1024 / 1024;
                var pressurePercent = (double)memoryUsageMB / _settings.TargetMemoryUsageMB * 100;
                
                return pressurePercent;
            }
            catch
            {
                return 0.0;
            }
        }

        public bool IsOptimizationInProgress => _isOptimizationInProgress;
        public long LastMemoryUsage => _lastMemoryUsage;
        public DateTime LastOptimizationTime => _lastOptimizationTime;
    }
}
